pollen/
├── main.typ              # 主文件（入口）
├── template.typ           # 样式模板
├── heading.typ           # 标题样式
├── toc.typ              # 目录样式
├── chapters/            # 章节内容目录
│   ├── abstract.typ     # 摘要
│   ├── chapter01.typ    # 第一章
│   ├── chapter02.typ    # 第二章
│   ├── chapter03.typ    # 第三章
│   ├── chapter04.typ    # 第四章
│   ├── chapter05.typ    # 第五章
│   └── references.typ   # 参考文献
├── assets/              # 资源文件
│   ├── images/          # 图片
│   └── data/           # 数据文件
└── pollen.bib          # 参考文献数据库



## 📝 章节文件格式

每个章节文件需要导入标题样式，然后包含内容：

```typst
// chapter01.typ
#import "../heading.typ": *

#new-chapter()
#chapter[第一章 绪论]

#section[研究背景与意义]
这里是研究背景的内容...

#subsection[国内研究现状]
这里是三级标题的内容...

#subsubsection[具体方法]
这里是四级标题的内容，编号为 (a)、(b)...

#section[研究目标]
这里是研究目标的内容...
```


**重要提示**：

- 每个章节文件开头必须添加 `#import "../heading.typ": *`
- 可以复制 `chapter_template.typ` 作为新章节的起始模板

**标题层级说明**：

- `#chapter[...]` - 一级标题：第X章（手动编号，居中）
- `#section[...]` - 二级标题：X.Y（自动编号，加粗）
- `#subsection[...]` - 三级标题：X.Y.Z（自动编号，进入目录）
- `#subsubsection[...]` - 四级标题：(a)、(b)（字母编号，不进入目录）