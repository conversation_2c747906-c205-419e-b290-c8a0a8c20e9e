// 表格使用示例
#import "settings/table.typ": styled-table, auto-width-table

= 表格示例

== 使用 styled-table (推荐)

这是最简单的使用方式，直接替换您原来的 table 函数：

#styled-table(
  columns: 6,
  [列1], [列2], [列3], [列4], [列5], [列6],
  [数据1], [数据2], [数据3], [数据4], [数据5], [数据6],
  [更多数据1], [更多数据2], [更多数据3], [更多数据4], [更多数据5], [更多数据6],
)

== 带标题的表格

如果您需要带编号的标题，可以添加 caption 参数：

#styled-table(
  columns: 6,
  caption: "实验数据统计表",
  [列1], [列2], [列3], [列4], [列5], [列6],
  [数据1], [数据2], [数据3], [数据4], [数据5], [数据6],
  [更多数据1], [更多数据2], [更多数据3], [更多数据4], [更多数据5], [更多数据6],
)

== 使用说明

- `styled-table` 函数会自动：
  - 让表格与页面等宽
  - 居中显示
  - 应用三线表样式（顶线、表头下线、底线）
  - 所有列等宽分布
  - 设置合适的内边距和字体大小

- 参数说明：
  - `columns`: 列数（必需）
  - `caption`: 表格标题（可选，如果提供会自动编号）
  - 后面跟表格内容，按行列顺序排列

- 您只需要将原来的 `#table(` 替换为 `#styled-table(`，其他保持不变！
