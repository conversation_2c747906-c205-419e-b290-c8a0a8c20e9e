// ================================================================
//                   第二章 空气致敏颗粒物在线监测的湿壁旋风采样方法学
// ================================================================
#import "../settings/heading.typ": *
#import "../settings/table.typ": *


#new-chapter()
#chapter[第二章 新型生物气溶胶采样系统的开发与性能研究]



// ================================================================
//                   2.1 引言
// ================================================================
#section[引言]
引言测试@LiuChangHaiJiYuShuJuZengQiangHeTeZhengRongHeDeHuaFenTuXiangFenLei2025@WangYaQunJiYuShuJuZengGuangHeTeZhengRongHeDeHuaFenXiBaoTuXiangShiBie2020@ZhangXinYueJiYuTuXiangChuLiDeHuaFenShenDuTuXiangXiuZhengFangFaYanJiu2023@WangLiHuaFenGuoMinDeYanJiuXianZhuangYuYingDuiCuoShi2025


本章将介绍与研究相关的技术背景...



// ================================================================
//                   2.2 材料与方法
// ================================================================
#section[材料与方法]

#subsection[实验材料与仪器]

详细介绍技术原理A...




// ================================================================
//                   2.2.1 新型湿壁气旋采样系统的设计与构建
// ================================================================
#subsection[新型湿壁气旋采样系统的设计与构建]
#subsubsection[花粉样本载玻片制备方法]
详细介绍技术原理B...
#subsubsection[显微图像采集参数优化]

详细介绍技术原理B...
#subsubsection[图像质量控制标准]
详细介绍技术原理B...





// ================================================================
//                   2.2.2 基于DPM的采样部署优化策略研究
// ================================================================
#subsection[基于DPM的采样部署优化策略研究]
#subsubsection[花粉样本载玻片制备方法]
详细介绍技术原理B...
#subsubsection[显微图像采集参数优化]

详细介绍技术原理B...
#subsubsection[图像质量控制标准]
详细介绍技术原理B...
// ================================================================
//                   2.2.3 采样器表面的超亲水表面改性
// ================================================================
#subsection[采样器表面的超亲水表面改性]


详细介绍技术原理B...



// ================================================================
//                   2.2.4 基于VOF的内壁液膜动力学优化策略研究
// ================================================================
#subsection[基于VOF的内壁液膜动力学优化策略研究]

#subsubsection[模型选择与网络架构配置]

详细介绍技术原理B...

#subsubsection[超参数选择与调优]
详细介绍技术原理B...

#subsubsection[损失函数设计]

详细介绍技术原理B...
#subsubsection[训练策略与收敛分析]

详细介绍技术原理B...
// ================================================================
//                   2.2.5 超亲水改性与液膜动力学机制
// ================================================================
#subsection[实验设计与性能评估]


#subsubsection[人类专家识别流程]
详细介绍技术原理B...

#subsubsection[染色对照组试验方法]

详细介绍技术原理B...
#subsubsection[评价指标体系建立]
详细介绍技术原理B...


// ================================================================
//                   2.2.6 采样系统性能的实验评估方案
// ================================================================
#subsection[采样系统性能的实验评估方案]


#subsubsection[人类专家识别流程]
详细介绍技术原理B...

#subsubsection[染色对照组试验方法]

详细介绍技术原理B...
#subsubsection[评价指标体系建立]
详细介绍技术原理B...



// ================================================================
//                   第三节 结果与讨论
// ================================================================
#section[结果与讨论]


// ================================================================
//                   2.3.1 真实场景下采样部署策略的现场验证
// ================================================================
#subsection[模型训练与验证结果分析]

#subsubsection[14种花粉形态学特征统计]
详细介绍技术原理B...
详细介绍技术原理B...详细介绍技术原理B...详细介绍技术原理B...
详细介绍技术原理B...
#load-csv-simple("../assets/data/result.csv", caption: "实验结果")



// ================================================================
//                   2.3.2 液膜稳定性对捕获效率影响的数值模拟
// ================================================================

#subsection[液膜稳定性对捕获效率影响的数值模拟]






// ================================================================
//                   2.3.3 超亲水改性对采样性能提升的实验研究
// ================================================================

#subsection[超亲水改性对采样性能提升的实验研究]


#subsubsection[对致敏真菌样本采集灵敏度的影响]
详细介绍技术原理B...

#subsubsection[对致敏真菌样本采集灵敏度的影响]





// 设置为横向页面
#page(flipped: true)[

#load-csv-simple("../assets/data/result.csv", caption: "实验结果")


#load-csv-simple("../assets/data/line.csv", caption: "实验结果")

#load-csv-simple("../assets/data/line.csv", caption: "实验结果")

]





// 你的横向内容（比如宽表格、图表等）
#table(
  columns: 6,
  [列1], [列2], [列3], [列4], [列5], [列6],
  [数据1], [数据2], [数据3], [数据4], [数据5], [数据6],
  // ... 更多数据
)

#load-csv-simple("../assets/data/line.csv", caption: "实验结果")


本研究中使用的主要工具包括...






// ================================================================
//                   第四节 本章小结
// ================================================================
#section[结论与展望]

当前技术的发展趋势分析...
