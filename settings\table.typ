// ================================================================
//                表格处理文件 (Table Processing)
// ================================================================

// --- 全局表格计数器 ---
#let table-counter = counter("global-table")

// --- 简化三线表样式设置 ---
#let three-line-table(
  data,
  caption: none
) = {
  // 步进表格计数器
  table-counter.step()
  
  // 获取当前章节号和表格序号
  let chapter-num = context counter(heading).display("1")
  let table-num = context table-counter.display("1")
  
  // 设置表格样式
  set text(size: 10.5pt)
  
  // 先显示标题
  context [
    #align(center)[
      // === 修改部分 开始 ===
      // 修复了变量名和连字符连接时产生的解析错误
      #text(weight: "bold")[表 #{chapter-num}-#{table-num}: #caption]
      // === 修改部分 结束 ===
    ]
  ]
  
  v(0.5em)
  
  // 再显示表格
  align(center)[
    #table(
      // 使用 `1fr` 让所有列平分页面宽度，从而实现自动换行
      columns: (1fr,) * data.at(0).len(),

      // 对于多行文本，建议使用 `horizon` (水平对齐到起点)
      align: horizon,

      inset: 8pt,
      stroke: none, // 将stroke移到下方统一处理
      
      // 表头
      table.header(
        // 为保持表头居中，可在此处单独设置
        ..data.at(0).map(cell => align(center, strong(cell))),
      ),

      // 表格的水平线 (三线表)
      table.hline(y: 0, stroke: 1.5pt), // 表格顶线，加粗
      table.hline(y: 1, stroke: 0.8pt), // 表头下线
      table.hline(y: data.len(), stroke: 1.5pt), // 表格底线，加粗
      
      // 数据行
      ..data.slice(1).flatten()
    )
  ]
}

// --- CSV读取函数 ---
#let load-csv-simple(
  file-path,
  caption: none
) = {
  let data = csv(file-path)
  three-line-table(data, caption: caption)
}

// --- 示例：result.csv数据表格 ---
#let result-table(caption: "实验结果数据") = {
  load-csv-simple("../assets/data/result.csv", caption: caption)
}

// --- 手动创建表格 ---
#let manual-table(
  headers,
  rows,
  caption: none
) = {
  let data = (headers,) + rows
  three-line-table(data, caption: caption)
}