// ================================================================
//                    目录样式文件 (Table of Contents)
// ================================================================

// --- 目录样式定义 ---
#let custom-toc() = {
  // 设置目录的全局段落样式，明确禁用可能影响居中的设置
  set par(leading: 12pt, spacing: 0em, first-line-indent: 0em, justify: false)
  
  // 一级标题：居中，16pt，加粗，解决长标题换行对齐问题
  show outline.entry.where(level: 1): it => {
    v(0.5em)

    
    set text(16pt, weight: "bold")
    set par(justify: false, first-line-indent: 0em)
    align(center, it)
    
    
    v(0.5em)
  }
  
  // 二级标题：左对齐，16pt，加粗
  show outline.entry.where(level: 2): it => {
    v(0.5em); grid(columns: (3em, 1fr), [], text(14pt, weight: "bold")[#it]); v(0.5em)
  }
  
  // 三级标题：缩进2字符，16pt，正常
  show outline.entry.where(level: 3): it => {
    v(0.5em); grid(columns: (5em, 1fr), [], text(14pt)[#it]); v(0.5em)
  }
  
  outline(title: none, depth: 3, indent: 0em)
}

// --- 目录标题函数 ---
#let toc-title() = {
  align(center, text(20pt, weight: "bold")[目录])
  v(1em)
}
